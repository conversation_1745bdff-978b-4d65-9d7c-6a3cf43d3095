from typing import List, Optional, Union
from pydantic import BaseModel, HttpUrl, Field, field_validator, ConfigDict
import html


class UrlDepthItem(BaseModel):
    url_depth: int
    urls: List[str]  # Changed from HttpUrl to str to handle HTML-encoded URLs

    @field_validator('urls', mode='before')
    @classmethod
    def decode_html_entities(cls, v):
        """Decode HTML entities in URLs (e.g., &amp; to &)"""
        if isinstance(v, list):
            return [html.unescape(url) if isinstance(url, str) else url for url in v]
        return v


class MccAnalysisRequest(BaseModel):
    website: str = Field(..., description="URL of the website to analyze")
    scrapeRequestRefID: str = Field(..., description="Reference ID for the scrape request")
    parsed_urls: List[UrlDepthItem] = Field(..., description="Parsed URLs with depth information")
    org_id: Optional[Union[str, int]] = Field(default="default", description="Organization ID")

    @field_validator('org_id', mode='before')
    @classmethod
    def convert_org_id_to_string(cls, v):
        """Convert org_id to string if it's an integer"""
        if v is not None:
            return str(v)
        return "default"

    class Config:
        json_schema_extra = {
            "example": {
                "website": "example.com",
                "scrapeRequestRefID": "REQ12345",
                "parsed_urls": [
                    {
                        "url_depth": 1,
                        "urls": ["https://example.com/url1", "https://example.com/url2"],
                    },
                    {
                        "url_depth": 2,
                        "urls": ["https://example.com/url3", "https://example.com/url4"],
                    },
                ],
                "org_id": "1234567890",
            }
        }

