from datetime import datetime
import time
from typing import Optional

from sqlalchemy import Column, types
from sqlmodel import Field, SQLModel
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import create_engine

from app.config import settings

#dao

# Create engine and session
engine = create_engine(settings.DATABASE_URL)
Session = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_current_time():
    return datetime.now().isoformat() + "Z"  # Return ISO format string with Z suffix


# Define a custom MEDIUMTEXT type
class MediumText(types.TypeDecorator):
    impl = types.TEXT
    cache_ok = True  # Set cache_ok to True for better performance

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def load_dialect_impl(self, dialect):
        if dialect.name == "mysql":
            return dialect.type_descriptor(types.TEXT(length=16777215))
        else:
            return dialect.type_descriptor(types.TEXT())


class MccAnalysis(SQLModel, table=True):
    """
    MCC Analysis model - stores MCC analysis results
    """
    __tablename__ = 'mcc_analysis_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    website: str
    scrape_request_ref_id: str
    result_status: Optional[str] = Field(default=None)
    mcc_code: Optional[str] = Field(default=None)  # Single MCC code
    business_category: Optional[str] = Field(default=None)
    business_description: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    reasoning: Optional[str] = Field(default=None, sa_column=Column(MediumText))  # Reasoning for MCC classification
    created_at: Optional[str] = Field(default_factory=get_current_time)
    started_at: Optional[str] = Field(default=None)
    completed_at: Optional[str] = Field(default=None)
    failed_at: Optional[str] = Field(default=None)
    last_updated: Optional[str] = Field(default=None)
    error_message: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    details: Optional[str] = Field(default=None, sa_column=Column(MediumText))  
    org_id: str = Field(default="default")
    processing_status: str = Field(default="PENDING")  # PENDING, PROCESSING, COMPLETED, FAILED


class WebsiteUrls(SQLModel, table=True):
    __tablename__ = 'website_urls_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    scrape_request_ref_id: str = Field(index=True)  # Changed from website_ref to scrape_request_ref_id
    website: str
    url: str = Field(default="", sa_column=Column(MediumText))
    depth: int = 1
    soft_class: str = ""
    hard_class: str = ""  # New field for hard classification results
    priority_url: bool = False
    extracted_text: str = Field(default="", sa_column=Column(MediumText))
    img_url: str = Field(default="", sa_column=Column(MediumText))
    policy: str = Field(default="", sa_column=Column(MediumText))
    registered_name: str = Field(default="", sa_column=Column(MediumText))
    org_id: str = Field(default="default", nullable=True)

#Todo: combine tables GeminiApiLog and GeneralLogs
class GeneralLogs(SQLModel, table=True):
    __tablename__ = 'general_logs_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    analysis_id: int
    timestamp: str
    type: str
    messages: str = Field(sa_column=Column(MediumText))
    response: str = Field(sa_column=Column(MediumText))
    org_id: str = Field(default="default", nullable=True)

class GeminiApiLog(SQLModel, table=True):
    __tablename__ = 'gemini_api_log_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    request_id: str
    org_id: str = Field(default="default", nullable=True)
    model_name: str
    task_type: str  # mcc_analysis, policy_analysis, etc.
    input_text: str = Field(sa_column=Column(MediumText))
    output_text: str = Field(sa_column=Column(MediumText))
    prompt_token_count: int
    completion_token_count: int
    total_token_count: int
    cached_token_count: Optional[int] = Field(default=0, nullable=True)
    usage_metadata: str = Field(default="", sa_column=Column(MediumText))
    http_status: int = Field(default=200, nullable=True)
    error_message: Optional[str] = Field(default=None, nullable=True)
    request_time: str = Field(default_factory=get_current_time)
    response_time: Optional[str] = Field(default=None, nullable=True)
    processing_time_ms: Optional[int] = Field(default=None, nullable=True)
    website_url: Optional[str] = Field(default=None, nullable=True)
    cost_estimate: Optional[float] = Field(default=None, nullable=True)
    is_cached: bool = Field(default=False)


# New model to track all scrape request references across different analysis types
class ScrapeRequestTracker(SQLModel, table=True):
    __tablename__ = 'scrape_request_tracker_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    scrape_request_ref_id: str = Field(index=True)  # Indexed for faster lookups
    analysis_type: str  # Type of analysis: "mcc", "risky", "policy", etc.
    website: str
    analysis_id: Optional[int] = Field(default=None)  # ID of the associated analysis record
    created_at: str = Field(default_factory=get_current_time)
    completed_at: Optional[str] = Field(default=None, nullable=True)
    status: str = "PENDING"  # PENDING, PROCESSING, COMPLETED, FAILED
    error_message: Optional[str] = Field(default=None, nullable=True)
    org_id: str = Field(default="default", nullable=True)

# Add the missing ScrapedUrl model
class ScrapedUrl(SQLModel, table=True):
    __tablename__ = 'scraped_urls_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    url: str = Field(sa_column=Column(MediumText))
    source_website: str
    created_at: str = Field(default_factory=get_current_time)
    analyzed: bool = False
    org_id: str = Field(default="default", nullable=True)

# Add Website model
class Website(SQLModel, table=True):
    __tablename__ = 'websites_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    url: str = Field(index=True)
    name: str
    created_at: str = Field(default_factory=get_current_time)
    last_updated: str = Field(default_factory=get_current_time)
    org_id: str = Field(default="default", nullable=True)


# Add MCC URL Classification model for detailed URL classification tracking
class MccUrlClassification(SQLModel, table=True):
    __tablename__ = 'mcc_url_classification_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    mcc_analysis_id: int = Field(index=True)  # References MccAnalysis.id
    scrape_request_ref_id: str = Field(index=True)
    url: str = Field(sa_column=Column(MediumText))
    url_depth: int = Field(default=1)
    soft_classification: str = ""  # Category from soft classification
    hard_classification: str = ""  # Category from hard classification
    final_classification: str = ""  # Final assigned category
    is_priority_url: bool = Field(default=False)
    is_social_media: bool = Field(default=False)
    created_at: str = Field(default_factory=get_current_time)
    org_id: str = Field(default="default", nullable=True)

