from fastapi import <PERSON><PERSON><PERSON>, Request, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from app.routers import mcc_analysis, error_notification
from app.utils.logger import setup_request_logging_middleware

from app.database import init_db, init_db_sync, database
import asyncio
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize the FastAPI app
app = FastAPI(title="FastAPI Service", version="1.0")

# Setup request logging middleware for request correlation
setup_request_logging_middleware(app)
logger.info("Request logging middleware initialized and ready to capture requests")

# Configure CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["34.47.244.221"],  # For production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database
@app.on_event("startup")
async def on_startup():
    logger.info("Initializing application")
    try:
        # Connect to async database
        await database.connect()
        logger.info("Async database connected successfully")

        # Initialize database tables (use sync version for table creation)
        init_db_sync()
        logger.info("Database tables initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        # Don't raise here - just log the error so we can still start the app
        # and serve the health check endpoints, etc.

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Application shutting down")
    try:
        await database.disconnect()
        logger.info("Database disconnected successfully")
    except Exception as e:
        logger.error(f"Error disconnecting database: {e}")
    pass

@app.middleware("http")
async def log_request_body(request: Request, call_next):
    body = await request.body()
    logger.debug(f"Raw Request Body: {body.decode('utf-8')}")
    request = Request(request.scope, asyncio.StreamReader())
    request._receive = lambda: {"type": "http.request", "body": body}
    return await call_next(request)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "ok", "message": "Service is running"}

# Include routers - MCC-only setup
app.include_router(mcc_analysis.router, prefix="/mcc-analysis", tags=["MccAnalysis"])
app.include_router(error_notification.router, prefix="/error-notification", tags=["ErrorNotification"])

