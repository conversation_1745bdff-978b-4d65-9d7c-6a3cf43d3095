"""
Simplified Gemini utils module with unified logging
"""
import json
from typing import Dict, Any, Optional
import os
import time
import dotenv
import google.generativeai as genai
from google.generativeai import types
from app.utils.logger import ConsoleLogger

dotenv.load_dotenv(".env")

# Configure the Gemini API key
if os.getenv("GEMINI"):
    genai.configure(api_key=os.getenv("GEMINI"))

# Default configuration constants
DEFAULT_MODEL = "gemini-1.5-flash"
DEFAULT_TIMEOUT = 60
DEFAULT_MAX_RETRIES = 2
DEFAULT_MAX_OUTPUT_TOKENS = 8000
DEFAULT_TEMPERATURE = 0
DEFAULT_TOP_P = 0.95

class GeminiClient:
    """Simplified Gemini client with unified error handling and logging"""
    
    def __init__(
        self, 
        context_name: str = "gemini", 
        request_id: Optional[str] = None,
        model_name: str = DEFAULT_MODEL
    ):
        """
        Initialize Gemini client
        
        Args:
            context_name: Name for logging context
            request_id: Optional request ID for tracking
            model_name: Gemini model name to use
        """
        self.logger = ConsoleLogger(context_name)
        self.model_name = model_name
        
    def clean_json_response(self, response: str) -> Dict[str, Any]:
        """
        Clean and parse JSON response
        
        Args:
            response: Raw response string
            
        Returns:
            Dict[str, Any]: Parsed JSON or default error response
        """
        try:
            if not response or not isinstance(response, str):
                self.logger.error("Invalid response type or empty response")
                return {"error": "Invalid or empty response", "success": False}
            
            response = response.strip()
            
            # Handle markdown code blocks
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            elif "```" in response:
                json_str = response.split("```")[1].split("```")[0].strip()
            else:
                json_str = response
            
            parsed_json = json.loads(json_str)
            return parsed_json
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON decode error: {str(e)}", data={"response_sample": response[:200]})
            return {"error": f"JSON parsing failed: {str(e)}", "success": False, "raw_response": response[:500]}
        except Exception as e:
            self.logger.error(f"Unexpected error in JSON parsing: {str(e)}")
            return {"error": f"Unexpected parsing error: {str(e)}", "success": False}

    async def generate_content(
        self,
        prompt: str,
        task_type: str = "general",
        website_url: Optional[str] = None,
        temperature: float = DEFAULT_TEMPERATURE,
        max_output_tokens: int = DEFAULT_MAX_OUTPUT_TOKENS,
        timeout_seconds: int = DEFAULT_TIMEOUT,
        max_retries: int = DEFAULT_MAX_RETRIES,
        enable_url_context: bool = True,
        enable_google_search: bool = False,
        org_id: str = "default"
    ) -> Dict[str, Any]:
        """
        Generate content using Gemini API
        
        Args:
            prompt: The text prompt to send to Gemini
            task_type: Type of task (classification, analysis, etc.)
            website_url: Optional website URL for context
            temperature: Model temperature (0-1)
            max_output_tokens: Maximum output tokens
            timeout_seconds: Timeout in seconds
            max_retries: Maximum retry attempts
            enable_url_context: Whether to enable URL context tool
            enable_google_search: Whether to enable Google search tool
            org_id: Organization ID
            
        Returns:
            Dict with response text and token usage information
        """
        if not os.getenv("GEMINI"):
            self.logger.error("GEMINI API key not found in environment")
            return {"error": "GEMINI API key not configured", "success": False}
        
        if not prompt:
            self.logger.error("Empty prompt provided")
            return {"error": "Empty prompt", "success": False}
        
        self.logger.info(f"Starting Gemini API call for {task_type}", {
            "model": self.model_name,
            "website": website_url,
            "temperature": temperature,
            "max_tokens": max_output_tokens
        })
        
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.logger.info(f"Gemini API attempt {retry_count + 1}/{max_retries}")
                
                # Configure tools based on settings
                tools = []
                if enable_url_context:
                    tools.append(types.Tool(url_context=types.UrlContext()))
                if enable_google_search:
                    tools.append(types.Tool(google_search=types.GoogleSearch()))
                
                # Initialize model
                model = genai.GenerativeModel(self.model_name)
                
                # Make API call with tool configuration
                response = model.generate_content(
                    contents=prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=temperature,
                        top_p=DEFAULT_TOP_P,
                        candidate_count=1,
                        max_output_tokens=max_output_tokens,
                    ),
                    tools=tools,
                    request_options={"timeout": timeout_seconds}
                )
                
                # Extract token usage
                token_info = {
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "total_tokens": 0
                }
                
                if hasattr(response, 'usage_metadata') and response.usage_metadata:
                    try:
                        if hasattr(response.usage_metadata, 'prompt_token_count'):
                            token_info["prompt_tokens"] = response.usage_metadata.prompt_token_count
                        if hasattr(response.usage_metadata, 'candidates_token_count'):
                            token_info["completion_tokens"] = response.usage_metadata.candidates_token_count
                        if hasattr(response.usage_metadata, 'total_token_count'):
                            token_info["total_tokens"] = response.usage_metadata.total_token_count
                    except Exception as token_error:
                        self.logger.warning(f"Error extracting token info: {str(token_error)}")
                
                # Log API call with available data
                self.logger.info(
                    f"API call to {self.model_name} for {task_type} completed successfully",
                    {
                        "model": self.model_name,
                        "task_type": task_type,
                        "tokens": token_info,
                        "website_url": website_url,
                        "org_id": org_id
                    }
                )
                
                # Save to database through gpt_logger if available
                try:
                    from app.gpt_models.gpt_logger import log_gemini_usage
                    log_gemini_usage(
                        model_name=self.model_name,
                        task_type=task_type,
                        input_text=prompt,
                        output_text=response.text if hasattr(response, 'text') else "",
                        usage_metadata=response.usage_metadata if hasattr(response, 'usage_metadata') else None,
                        website_url=website_url,
                        org_id=org_id
                    )
                except ImportError:
                    self.logger.warning("Could not import log_gemini_usage, skipping database logging")
                except Exception as log_error:
                    self.logger.warning(f"Error logging Gemini usage to database: {str(log_error)}")
                
                # Return response text and token info
                return {
                    "text": response.text if hasattr(response, 'text') else "",
                    "success": True,
                    "token_info": token_info
                }
                
            except Exception as e:
                retry_count += 1
                self.logger.error(f"Gemini API error on attempt {retry_count}: {str(e)}")
                
                # Wait before retrying
                if retry_count < max_retries:
                    wait_time = 2 * retry_count
                    self.logger.info(f"Waiting {wait_time}s before retry...")
                    time.sleep(wait_time)
        
        # All retries exhausted
        error_msg = f"Failed to get response after {max_retries} attempts"
        self.logger.error(error_msg)
        return {"error": error_msg, "success": False}

    def classify_mcc(
        self, 
        website_url: str, 
        website_info: Dict[str, Any], 
        prompt: str
    ) -> Dict[str, Any]:
        """
        Specialized method for MCC classification
        
        Args:
            website_url: Website URL
            website_info: Website information
            prompt: Classification prompt
            
        Returns:
            Classification result
        """
        response = self.generate_content(
            prompt=prompt,
            task_type="mcc_classification",
            website_url=website_url,
            temperature=0,
            max_output_tokens=4000,
            enable_url_context=True,
            enable_google_search=False
        )
        
        if not response.get("success", False):
            return {"error": response.get("error", "Unknown error"), "success": False}
        
        # Parse JSON response
        parsed_result = self.clean_json_response(response["text"])
        
        # Add metadata
        parsed_result["website"] = website_url
        parsed_result["token_info"] = response.get("token_info", {})
        
        return parsed_result


# Simple function to get a client instance
def get_gemini_client(
    context_name: str = "gemini", 
    request_id: Optional[str] = None,
    model_name: str = DEFAULT_MODEL
) -> GeminiClient:
    """
    Get a Gemini client instance
    
    Args:
        context_name: Context name for logging
        request_id: Optional request ID
        model_name: Model name
        
    Returns:
        GeminiClient instance
    """
    return GeminiClient(context_name, request_id, model_name)
