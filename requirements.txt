aiohappyeyeballs==2.4.6
aiohttp
aiosignal==1.3.2
alembic==1.14.1
amqp==5.3.1
annotated-types==0.7.0
anyio==4.8.0
asttokens==3.0.0
asyncio==3.4.3
attrs==25.1.0
azure-core==1.32.0
azure-storage-blob==12.24.1
beautifulsoup4==4.13.3 
# boto3==1.36.4
# botocore==1.36.4
bs4==0.0.2
cachetools==5.5.1
certifi==2024.12.14
cffi==1.17.1
cfgv==3.4.0
charset-normalizer==3.4.1
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cryptography==44.0.0
decorator==5.1.1
distlib==0.3.9
distro==1.9.0
executing==2.2.0
fastapi==0.115.7
filelock==3.17.0
frozenlist==1.5.0
glob2==0.7
google-api-core==2.24.1
google-auth==2.38.0
google-cloud-core==2.4.1
google-cloud-storage==3.0.0
google-crc32c==1.6.0
google-resumable-media==2.7.2
googleapis-common-protos==1.66.0
google-generativeai==0.8.3
greenlet==3.1.1
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
humanize==4.11.0
identify==2.6.6
idna==3.10
ipython==8.31.0
isodate==0.7.2
jedi==0.19.2
jiter==0.8.2
jmespath==1.0.1
kombu==5.4.2
Mako==1.3.8
MarkupSafe==3.0.2
matplotlib-inline==0.1.7
multidict==6.1.0
mysql-connector-python==9.2.0
aiomysql==0.2.0
aiosqlite==0.21.0
databases[mysql]==0.9.0
nodeenv==1.9.1
numpy==2.2.2
openai==1.60.0
pandas==2.2.3
parso==0.8.4
pexpect==4.9.0
pika==1.3.2
pillow==11.1.0
platformdirs==4.3.6
playwright==1.49.1
psutil>=5.9.8
pre_commit==4.1.0
prometheus_client==0.21.1
prompt_toolkit==3.0.50
propcache==0.3.0
proto-plus==1.26.0
protobuf==5.29.3
ptyprocess==0.7.0
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.10.5
pydantic-settings==2.7.1
pydantic_core==2.27.2
pyee==12.0.0
Pygments==2.19.1
PyMySQL==1.1.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2024.2
PyYAML==6.0.2
regex
requests==2.32.3
rsa==4.9
s3transfer==0.11.1
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.37
sqlmodel==0.0.22
stack-data==0.6.3
starlette==0.45.3
structlog==25.1.0
tiktoken==0.8.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
tldextract==5.1.1
typing_extensions==4.12.2
tzdata==2025.1
urllib3==2.3.0
uvicorn==0.34.0
vine==5.1.0
virtualenv==20.29.1
wcwidth==0.2.13
yarl==1.18.3
google-genai
