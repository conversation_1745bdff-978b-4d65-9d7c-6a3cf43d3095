name: CI/CD Pipeline

on:
  push:
    branches: [ main, mcc-only, my-local-changes ]
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.gitignore'
  pull_request:
    branches: [ main, mcc-only ]
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.gitignore'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Lint and Test Stage
  lint-and-test:
    name: Lint and <PERSON>
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install flake8 black isort pytest pytest-asyncio
        
    - name: Lint with flake8
      run: |
        # Stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # Exit-zero treats all errors as warnings
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
        
    - name: Check code formatting with black
      run: |
        black --check --diff .
        
    - name: Check import sorting with isort
      run: |
        isort --check-only --diff .
        
    - name: Run tests
      run: |
        pytest tests/ -v --cov=app --cov-report=xml --cov-report=term-missing
      continue-on-error: true
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        fail_ci_if_error: false
      continue-on-error: true

  # Security Scan Stage
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Bandit security scan
      run: |
        pip install bandit
        bandit -r app/ -f json -o bandit-report.json || true
        
    - name: Run Safety check
      run: |
        pip install safety
        safety check --json --output safety-report.json || true
        
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json
        retention-days: 30

  # Build and Test Docker Image
  build-and-test:
    name: Build and Test Docker Image
    runs-on: ubuntu-latest
    needs: [lint-and-test]
    timeout-minutes: 20
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
          
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Test Docker image
      run: |
        docker run --rm ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} python -c "import app; print('App imports successfully')"
        
    - name: Test health endpoint
      run: |
        docker run -d --name test-app -p 8000:8000 ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        sleep 10
        curl -f http://localhost:8000/health || exit 1
        docker stop test-app
        docker rm test-app

  # Deploy to Staging (for main branch)
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-and-test]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: staging
    timeout-minutes: 15
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to Google Cloud Run (Staging)
      uses: google-github-actions/deploy-cloudrun@v2
      with:
        service: webreview-ds-api-staging
        image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        region: us-central1
        env_vars: |
          ENVIRONMENT=staging
        secrets: |
          DATABASE_URL=${{ secrets.STAGING_DATABASE_URL }}
          GOOGLE_CLOUD_PROJECT=${{ secrets.GOOGLE_CLOUD_PROJECT }}
      env:
        PROJECT_ID: ${{ secrets.GOOGLE_CLOUD_PROJECT }}
        WORKLOAD_IDENTITY_PROVIDER: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
        SERVICE_ACCOUNT: ${{ secrets.SERVICE_ACCOUNT }}
        
    - name: Run smoke tests
      run: |
        # Wait for deployment to be ready
        sleep 30
        # Test health endpoint
        curl -f ${{ steps.deploy.outputs.url }}/health || exit 1

  # Deploy to Production (manual trigger or release)
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-and-test]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    timeout-minutes: 20
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to Google Cloud Run (Production)
      uses: google-github-actions/deploy-cloudrun@v2
      with:
        service: webreview-ds-api-prod
        image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        region: us-central1
        env_vars: |
          ENVIRONMENT=production
        secrets: |
          DATABASE_URL=${{ secrets.PROD_DATABASE_URL }}
          GOOGLE_CLOUD_PROJECT=${{ secrets.GOOGLE_CLOUD_PROJECT }}
      env:
        PROJECT_ID: ${{ secrets.GOOGLE_CLOUD_PROJECT }}
        WORKLOAD_IDENTITY_PROVIDER: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
        SERVICE_ACCOUNT: ${{ secrets.SERVICE_ACCOUNT }}
        
    - name: Run production tests
      run: |
        # Wait for deployment to be ready
        sleep 30
        # Test health endpoint
        curl -f ${{ steps.deploy.outputs.url }}/health || exit 1
        # Test main functionality
        curl -f ${{ steps.deploy.outputs.url }}/mcc-analysis/health || exit 1

  # Notify on failure
  notify-failure:
    name: Notify on Failure
    runs-on: ubuntu-latest
    if: failure()
    needs: [lint-and-test, build-and-test, deploy-staging, deploy-production]
    
    steps:
    - name: Notify failure
      run: |
        echo "Pipeline failed! Check the logs for details."
        # Add your notification logic here (Slack, email, etc.) 