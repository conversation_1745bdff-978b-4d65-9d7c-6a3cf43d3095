name: Manual Deploy

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      image_tag:
        description: 'Docker image tag to deploy'
        required: false
        default: 'latest'
        type: string
  release:
    types: [published]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  deploy:
    name: Deploy to ${{ github.event.inputs.environment || 'production' }}
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'production' }}
    timeout-minutes: 30
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ secrets.GOOGLE_CLOUD_PROJECT }}
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        export_default_credentials: true
        
    - name: Configure Docker for GCR
      run: gcloud auth configure-docker
      
    - name: Deploy to Google Cloud Run
      id: deploy
      uses: google-github-actions/deploy-cloudrun@v2
      with:
        service: webreview-ds-api-${{ github.event.inputs.environment || 'prod' }}
        image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.event.inputs.image_tag || github.ref_name }}
        region: us-central1
        env_vars: |
          ENVIRONMENT=${{ github.event.inputs.environment || 'production' }}
          VERSION=${{ github.ref_name }}
        secrets: |
          DATABASE_URL=${{ github.event.inputs.environment == 'staging' && secrets.STAGING_DATABASE_URL || secrets.PROD_DATABASE_URL }}
          GOOGLE_CLOUD_PROJECT=${{ secrets.GOOGLE_CLOUD_PROJECT }}
          
    - name: Run health checks
      run: |
        echo "Deployment URL: ${{ steps.deploy.outputs.url }}"
        # Wait for deployment to be ready
        sleep 30
        # Test health endpoint
        curl -f ${{ steps.deploy.outputs.url }}/health || exit 1
        echo "Health check passed!"
        
    - name: Run smoke tests
      run: |
        # Test main functionality
        curl -f ${{ steps.deploy.outputs.url }}/mcc-analysis/health || exit 1
        echo "Smoke tests passed!"
        
    - name: Notify success
      run: |
        echo "Deployment to ${{ github.event.inputs.environment || 'production' }} successful!"
        echo "URL: ${{ steps.deploy.outputs.url }}" 