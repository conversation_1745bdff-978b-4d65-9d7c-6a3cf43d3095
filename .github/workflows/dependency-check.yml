name: Dependency Check and Security Scan

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:
  push:
    paths:
      - 'requirements.txt'
      - 'requirements-dev.txt'
      - 'pyproject.toml'
      - 'setup.py'

jobs:
  dependency-check:
    name: Check Dependencies
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install safety bandit pip-audit
        
    - name: Check for security vulnerabilities
      run: |
        echo "Running safety check..."
        safety check --json --output safety-report.json || true
        
        echo "Running pip-audit..."
        pip-audit --format json --output pip-audit-report.json || true
        
    - name: Check for outdated packages
      run: |
        echo "Checking for outdated packages..."
        pip list --outdated --format=json > outdated-packages.json || true
        
    - name: Upload reports
      uses: actions/upload-artifact@v3
      with:
        name: dependency-reports
        path: |
          safety-report.json
          pip-audit-report.json
          outdated-packages.json
        retention-days: 30
        
    - name: Create issue for vulnerabilities
      if: failure()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          
          try {
            const safetyReport = JSON.parse(fs.readFileSync('safety-report.json', 'utf8'));
            const pipAuditReport = JSON.parse(fs.readFileSync('pip-audit-report.json', 'utf8'));
            
            let issueBody = '## Security Vulnerability Report\n\n';
            
            if (safetyReport.length > 0) {
              issueBody += '### Safety Check Results\n';
              safetyReport.forEach(vuln => {
                issueBody += `- **${vuln.package}**: ${vuln.advisory}\n`;
              });
              issueBody += '\n';
            }
            
            if (pipAuditReport.vulns && pipAuditReport.vulns.length > 0) {
              issueBody += '### Pip Audit Results\n';
              pipAuditReport.vulns.forEach(vuln => {
                issueBody += `- **${vuln.package}**: ${vuln.description}\n`;
              });
            }
            
            if (safetyReport.length > 0 || (pipAuditReport.vulns && pipAuditReport.vulns.length > 0)) {
              await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: '🚨 Security Vulnerabilities Detected',
                body: issueBody,
                labels: ['security', 'dependencies']
              });
            }
          } catch (error) {
            console.log('No vulnerabilities found or error reading reports');
          }

  code-quality:
    name: Code Quality Check
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install bandit pylint mypy
        
    - name: Run Bandit security scan
      run: |
        bandit -r app/ -f json -o bandit-report.json || true
        
    - name: Run Pylint
      run: |
        pylint app/ --output-format=json > pylint-report.json || true
        
    - name: Run MyPy type checking
      run: |
        mypy app/ --json-report mypy-report.json || true
        
    - name: Upload quality reports
      uses: actions/upload-artifact@v3
      with:
        name: quality-reports
        path: |
          bandit-report.json
          pylint-report.json
          mypy-report.json
        retention-days: 30 