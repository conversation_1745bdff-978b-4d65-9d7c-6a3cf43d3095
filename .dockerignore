# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
*.md
docs/

# Tests
tests/
test_*

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
*.tmp

# Data files (if not needed in container)
*.csv
*.json
*.xml
*.txt
!requirements.txt
!pyproject.toml

# Large files
*.zip
*.tar.gz
*.rar

# Node modules (if any)
node_modules/

# Build artifacts
build/
dist/ 