build 
docker build -t name .

docker build -t mcc-server-optimized .

run
docker run --env-file .env -d -p 8000:8000 --name mcc-server-container mcc-cloud-run-latest



# 1. Set your Google Cloud Project ID
export PROJECT_ID=$(gcloud config get-value project)

# 2. Set the region for Artifact Registry (use the region where your repo is, e.g., asia-south1)
export REGION=asia-south1

  $ gcloud auth login

to obtain new credentials.

If you have already logged in with a different account, run:

  $ gcloud config set account ACCOUNT


echo "PROJECT_ID is: '$PROJECT_ID'"
echo "REGION is: '$REGION'"

tag
docker tag   mcc-server-optimized  "$REGION-docker.pkg.dev/$PROJECT_ID/cloud-run-repo/mcc-only:new02"

after pushed 
docker push "$REGION-docker.pkg.dev/$PROJECT_ID/cloud-run-repo/mcc-only:new02"


nohup uvicorn app.main:app --host 0.0.0.0 --port 8000 > server_619.log 2>&1 &


uvicorn app.main:app --host 0.0.0.0 --port 8000


gcloud auth list
    Credentialed Accounts
ACTIVE  ACCOUNT
*       <EMAIL>

To set the active account, run:
    $ gcloud config set account `ACCOUNT`

(v) rohitrathod@pop-os:~/Desktop/extractUrl$ SERVICE_URL=$(gcloud run services describe hello-world --platform managed --region asia-south1 --format 'value(status.url)')
ERROR: (gcloud.run.services.describe) Cannot find service [hello-world]
(v) rohitrathod@pop-os:~/Desktop/extractUrl$ SERVICE_URL=$(gcloud run services describe mcc-only --platform managed --region asia-south1 --format 'value(status.url)')
(v) rohitrathod@pop-os:~/Desktop/extractUrl$ TOKEN=$(gcloud auth print-identity-token)
(v) rohitrathod@pop-os:~/Desktop/extractUrl$ curl -H "Authorization: Bearer ${TOKEN}" "${SERVICE_URL}"